#include "headfile.h"
#define DATA_INPUT_USER  10      // ÿ��������󳤶�
#define AXIS_NUMBER      3       // ����ά�ȣ���ǰ�¶�,�趨�¶�,ʱ�䣩
#define RX_BUF_SIZE      128     // ���ջ�������С
#define FRAME_END_CHAR   '\n'    // ���ݰ�������
#define FRAME_TIMEOUT_MS 100     // ���ݰ���ʱʱ��(ms)

// ȫ�ֱ���
uint32_t uart_tick = 0;          // ���ڴ���ʱ���
uint32_t frame_start_tick = 0;   // ���ݰ���ʼʱ���
uint8_t rx_data = 0;             // ���ֽڽ��ջ���
uint8_t rx_buff[RX_BUF_SIZE];    // ���ջ�����
uint16_t rx_pointer = 0;         // ������ָ��
uint8_t data_ready = 0;          // ���ݾ�����־
uint8_t frame_complete = 0;      // ���ݰ������־
float input_user_buffer[DATA_INPUT_USER * AXIS_NUMBER]; // ���ݴ洢����
uint16_t buffer_index = 0;       // �洢����������
float temperature = 0.0f;
float setting = 0.0f;
float time_value = 0.0f;
// ���ݴ洢����input_user_buffer
void store_data_to_buffer(void)
{
    if (buffer_index < DATA_INPUT_USER) {
        uint16_t base_index = buffer_index * AXIS_NUMBER;
        input_user_buffer[base_index + 0] = temperature;  // ��ǰ�¶�
        input_user_buffer[base_index + 1] = setting;      // �趨�¶�
        input_user_buffer[base_index + 2] = time_value;   // ʱ��ֵ
        buffer_index++;
        printf("Data stored [%d]: T=%.1f, Set=%.1f, Time=%.1f\r\n",
               buffer_index-1, temperature, setting, time_value);
    } else {
        printf("Buffer full! Cannot store more data.\r\n");
    }
}

// ��ȡ�洢������
uint16_t get_stored_data_count(void)
{
    return buffer_index;
}

// ��ջ�����
void clear_data_buffer(void)
{
    buffer_index = 0;
    memset(input_user_buffer, 0, sizeof(input_user_buffer));
    printf("Data buffer cleared.\r\n");
}

uint8_t parse_data(void)
{
    char *token;
    char *endptr;
    char temp_buff[RX_BUF_SIZE]; // ��ʱ������ֹ�ƻ�ԭʼ����

    // ������ԭʼ����
    strncpy(temp_buff, (char*)rx_buff, RX_BUF_SIZE-1);
    temp_buff[RX_BUF_SIZE-1] = '\0';

    // ������һ��ֵ (��ǰ�¶�)
    token = strtok(temp_buff, ",");
    if (!token) return 0;
    temperature = strtof(token, &endptr);
    if (*endptr != '\0') return 0; // ��������

    // �����ڶ���ֵ (�趨�¶�)
    token = strtok(NULL, ",");
    if (!token) return 0;
    setting = strtof(token, &endptr);
    if (*endptr != '\0') return 0;

    // ����������ֵ (ʱ��)
    token = strtok(NULL, ",");
    if (!token) return 0;
    time_value = strtof(token, &endptr);
    if (*endptr != '\0') return 0;

    return 1; // �����ɹ�
}

void uart_proc(void)
{
    // ÿ10ms���һ��
    if(uwTick - uart_tick < 10) {
        return;
    }
    uart_tick = uwTick;

    // ���ݰ���ʱ����
    if(rx_pointer > 0 && (uwTick - frame_start_tick > FRAME_TIMEOUT_MS)) {
        printf("Frame timeout! Clearing buffer.\r\n");
        rx_pointer = 0;
        frame_complete = 0;
        memset(rx_buff, 0, RX_BUF_SIZE);
        return;
    }

    // ������ݰ�����
    if(frame_complete) {
        printf("Received: %s\r\n", rx_buff);

        if (parse_data()) {
            debug_parse_success_count++; // 调试计数
            printf("Parsed: T=%.1f°C, Set=%.1f°C, Time=%.1fs\r\n",
                   temperature, setting, time_value);

            // �洢���ݵ�����
            store_data_to_buffer();

        } else {
            debug_parse_error_count++; // 调试计数
            printf("ERROR: Invalid data format\r\n");
        }

        // ���ý���״̬
        rx_pointer = 0;
        frame_complete = 0;
        memset(rx_buff, 0, RX_BUF_SIZE);
    }
}

// printf重定向到UART4
#include <stdio.h>
struct __FILE
{
  int handle;
};
FILE __stdout;

int fputc(int ch, FILE *f)
{
  HAL_StatusTypeDef status;
  status = HAL_UART_Transmit(&huart4, (uint8_t *)&ch, 1, 100);
  if(status != HAL_OK) {
    // 如果发送失败，可以在这里处理错误
  }
  return ch;
}

// 调试计数器
uint32_t debug_uart_rx_count = 0;
uint32_t debug_frame_complete_count = 0;
uint32_t debug_parse_success_count = 0;
uint32_t debug_parse_error_count = 0;

// 添加调试LED指示函数（如果有LED的话）
void debug_toggle_led(void)
{
  // 如果有LED，可以在这里切换LED状态来指示程序运行
  // HAL_GPIO_TogglePin(LED_GPIO_Port, LED_Pin);
}

// 调试信息输出函数
void print_debug_info(void)
{
  printf("=== Debug Info ===\r\n");
  printf("UART RX Count: %lu\r\n", debug_uart_rx_count);
  printf("Frame Complete: %lu\r\n", debug_frame_complete_count);
  printf("Parse Success: %lu\r\n", debug_parse_success_count);
  printf("Parse Error: %lu\r\n", debug_parse_error_count);
  printf("Buffer Index: %d\r\n", buffer_index);
  printf("RX Pointer: %d\r\n", rx_pointer);
  printf("Frame Complete Flag: %d\r\n", frame_complete);
  printf("==================\r\n");
}

// 测试函数：发送测试数据
void send_test_data(void)
{
  printf("Sending test data...\r\n");
  // 模拟接收测试数据
  char test_data[] = "25.5,30.0,120.5";
  strcpy((char*)rx_buff, test_data);
  rx_pointer = strlen(test_data);
  frame_complete = 1;
  printf("Test data prepared: %s\r\n", rx_buff);
}

void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if(huart->Instance == UART4) {
        debug_uart_rx_count++; // 调试计数

        // 记录数据帧开始时间
        if(rx_pointer == 0) {
            frame_start_tick = uwTick;
        }

        // 检查缓冲区溢出
        if(rx_pointer >= RX_BUF_SIZE - 1) {
            printf("Buffer overflow! Resetting...\r\n");
            rx_pointer = 0;
            frame_complete = 0;
            memset(rx_buff, 0, RX_BUF_SIZE);
        } else {
            // 存储数据到缓冲区
            rx_buff[rx_pointer] = rx_data;

            // 检查帧结束符
            if(rx_data == FRAME_END_CHAR || rx_data == '\r') {
                rx_buff[rx_pointer] = '\0'; // 字符串结束符
                frame_complete = 1;
                debug_frame_complete_count++; // 调试计数
                printf("Frame received: %s (len=%d)\r\n", rx_buff, rx_pointer);
            } else {
                rx_pointer++;
                // 实时显示接收到的字符（用于调试）
                printf("RX[%d]: 0x%02X ('%c')\r\n", rx_pointer-1, rx_data,
                       (rx_data >= 32 && rx_data <= 126) ? rx_data : '?');
            }
        }

        // 继续接收下一个字节
        HAL_UART_Receive_IT(&huart4, &rx_data, 1);
    }
}
