#include "headfile.h"
#define DATA_INPUT_USER  10      // ÿ��������󳤶�
#define AXIS_NUMBER      3       // ����ά�ȣ���ǰ�¶�,�趨�¶�,ʱ�䣩
#define RX_BUF_SIZE      128     // ���ջ�������С
#define FRAME_END_CHAR   '\n'    // ���ݰ�������
#define FRAME_TIMEOUT_MS 100     // ���ݰ���ʱʱ��(ms)

// ȫ�ֱ���
uint32_t uart_tick = 0;          // ���ڴ���ʱ���
uint32_t frame_start_tick = 0;   // ���ݰ���ʼʱ���
uint8_t rx_data = 0;             // ���ֽڽ��ջ���
uint8_t rx_buff[RX_BUF_SIZE];    // ���ջ�����
uint16_t rx_pointer = 0;         // ������ָ��
uint8_t data_ready = 0;          // ���ݾ�����־
uint8_t frame_complete = 0;      // ���ݰ������־
float input_user_buffer[DATA_INPUT_USER * AXIS_NUMBER]; // ���ݴ洢����
uint16_t buffer_index = 0;       // �洢����������
float temperature = 0.0f;
float setting = 0.0f;
float time_value = 0.0f;
// 发送接收到的数据（回显确认）
void send_received_data(void)
{
    char send_buffer[64];

    // 格式化发送数据
    snprintf(send_buffer, sizeof(send_buffer),
             "ACK:%.1f,%.1f,%.1f\r\n", temperature, setting, time_value);

    // 通过UART发送
    HAL_UART_Transmit(&huart4, (uint8_t*)send_buffer, strlen(send_buffer), 100);

    printf("Sent ACK: %s", send_buffer);
}

// ���ݴ洢����input_user_buffer
void store_data_to_buffer(void)
{
    if (buffer_index < DATA_INPUT_USER) {
        uint16_t base_index = buffer_index * AXIS_NUMBER;
        input_user_buffer[base_index + 0] = temperature;  // ��ǰ�¶�
        input_user_buffer[base_index + 1] = setting;      // �趨�¶�
        input_user_buffer[base_index + 2] = time_value;   // ʱ��ֵ
        buffer_index++;

        printf("Data stored [%d]: T=%.1f, Set=%.1f, Time=%.1f\r\n",
               buffer_index-1, temperature, setting, time_value);

        // 发送接收确认
        send_received_data();

    } else {
        printf("Buffer full! Cannot store more data.\r\n");
        // 缓冲区满时也发送确认，但标记为错误
        char error_msg[] = "ACK:BUFFER_FULL\r\n";
        HAL_UART_Transmit(&huart4, (uint8_t*)error_msg, strlen(error_msg), 100);
    }
}

// ��ȡ�洢������
uint16_t get_stored_data_count(void)
{
    return buffer_index;
}

// ��ջ�����
void clear_data_buffer(void)
{
    buffer_index = 0;
    memset(input_user_buffer, 0, sizeof(input_user_buffer));
    printf("Data buffer cleared.\r\n");

    // 发送清空确认
    char clear_msg[] = "ACK:BUFFER_CLEARED\r\n";
    HAL_UART_Transmit(&huart4, (uint8_t*)clear_msg, strlen(clear_msg), 100);
}

// 发送所有存储的数据
void send_all_stored_data(void)
{
    char send_buffer[128];

    // 发送数据头
    snprintf(send_buffer, sizeof(send_buffer),
             "DATA_COUNT:%d\r\n", buffer_index);
    HAL_UART_Transmit(&huart4, (uint8_t*)send_buffer, strlen(send_buffer), 100);

    // 发送每组数据
    for(uint16_t i = 0; i < buffer_index; i++) {
        uint16_t base_index = i * AXIS_NUMBER;
        float temp = input_user_buffer[base_index + 0];
        float set = input_user_buffer[base_index + 1];
        float time = input_user_buffer[base_index + 2];

        snprintf(send_buffer, sizeof(send_buffer),
                 "DATA[%d]:%.1f,%.1f,%.1f\r\n", i, temp, set, time);
        HAL_UART_Transmit(&huart4, (uint8_t*)send_buffer, strlen(send_buffer), 100);

        HAL_Delay(10); // 短暂延时避免发送过快
    }

    // 发送结束标记
    char end_msg[] = "DATA_END\r\n";
    HAL_UART_Transmit(&huart4, (uint8_t*)end_msg, strlen(end_msg), 100);

    printf("All stored data sent (%d records)\r\n", buffer_index);
}

// 获取指定索引的数据
uint8_t get_data_by_index(uint16_t index, float *temp, float *set, float *time)
{
    if(index >= buffer_index) {
        return 0; // 索引超出范围
    }

    uint16_t base_index = index * AXIS_NUMBER;
    *temp = input_user_buffer[base_index + 0];
    *set = input_user_buffer[base_index + 1];
    *time = input_user_buffer[base_index + 2];

    return 1; // 成功
}

uint8_t parse_data(void)
{
    char *token;
    char *endptr;
    char temp_buff[RX_BUF_SIZE]; // ��ʱ������ֹ�ƻ�ԭʼ����

    // ������ԭʼ����
    strncpy(temp_buff, (char*)rx_buff, RX_BUF_SIZE-1);
    temp_buff[RX_BUF_SIZE-1] = '\0';

    // ������һ��ֵ (��ǰ�¶�)
    token = strtok(temp_buff, ",");
    if (!token) return 0;
    temperature = strtof(token, &endptr);
    if (*endptr != '\0') return 0; // ��������

    // �����ڶ���ֵ (�趨�¶�)
    token = strtok(NULL, ",");
    if (!token) return 0;
    setting = strtof(token, &endptr);
    if (*endptr != '\0') return 0;

    // ����������ֵ (ʱ��)
    token = strtok(NULL, ",");
    if (!token) return 0;
    time_value = strtof(token, &endptr);
    if (*endptr != '\0') return 0;

    return 1; // �����ɹ�
}

// UART重启函数
void uart_restart_receive(void)
{
    HAL_StatusTypeDef status;
    uint8_t retry_count = 0;

    // 停止当前接收
    HAL_UART_AbortReceive_IT(&huart4);
    HAL_Delay(1); // 短暂延时

    // 重试启动接收
    do {
        status = HAL_UART_Receive_IT(&huart4, &rx_data, 1);
        retry_count++;
        if(status != HAL_OK) {
            HAL_Delay(1);
        }
    } while(status != HAL_OK && retry_count < 5);

    if(status == HAL_OK) {
        printf("UART receive restarted successfully\r\n");
    } else {
        printf("UART receive restart failed! Status: %d\r\n", status);
    }
}

void uart_proc(void)
{
    // ÿ10ms���һ��
    if(uwTick - uart_tick < 10) {
        return;
    }
    uart_tick = uwTick;

    // 处理UART错误标志
    if(uart_error_flag) {
        if(uart_error_flag == 1) {
            printf("Buffer overflow! Resetting...\r\n");
        } else if(uart_error_flag == 2) {
            printf("UART Error detected! Restarting...\r\n");
        }
        uart_error_flag = 0;
    }

    // 处理UART重启标志
    if(uart_restart_flag) {
        uart_restart_flag = 0;
        uart_restart_receive();
        return;
    }

    // ���ݰ���ʱ����
    if(rx_pointer > 0 && (uwTick - frame_start_tick > FRAME_TIMEOUT_MS)) {
        printf("Frame timeout! Clearing buffer.\r\n");
        rx_pointer = 0;
        frame_complete = 0;
        memset(rx_buff, 0, RX_BUF_SIZE);
        return;
    }

    // ������ݰ�����
    if(frame_complete) {
        printf("Received: %s\r\n", rx_buff);

        // 首先尝试解析为数据
        if (parse_data()) {
            debug_parse_success_count++; // 调试计数
            printf("Parsed: T=%.1f°C, Set=%.1f°C, Time=%.1fs\r\n",
                   temperature, setting, time_value);

            // �洢���ݵ�����
            store_data_to_buffer();

        } else {
            // 如果不是数据格式，检查是否为命令
            if(strlen((char*)rx_buff) > 0) {
                // 移除可能的换行符
                char* newline = strchr((char*)rx_buff, '\r');
                if(newline) *newline = '\0';
                newline = strchr((char*)rx_buff, '\n');
                if(newline) *newline = '\0';

                // 处理命令
                process_command((char*)rx_buff);
            } else {
                debug_parse_error_count++; // 调试计数
                printf("ERROR: Invalid data format\r\n");

                // 发送错误响应
                char error_msg[] = "ERROR:INVALID_FORMAT\r\n";
                HAL_UART_Transmit(&huart4, (uint8_t*)error_msg, strlen(error_msg), 100);
            }
        }

        // ���ý���״̬
        rx_pointer = 0;
        frame_complete = 0;
        memset(rx_buff, 0, RX_BUF_SIZE);
    }

    // DMA模式处理
    if(use_dma_mode) {
        process_dma_data();
    }

    // 定期检查UART状态
    static uint32_t uart_check_tick = 0;
    static uint8_t error_count = 0;

    if(uwTick - uart_check_tick > 1000) { // 每秒检查一次
        uart_check_tick = uwTick;

        if(!use_dma_mode) {
            // 检查中断模式UART状态
            if(huart4.RxState != HAL_UART_STATE_BUSY_RX) {
                printf("UART RX not active! Restarting... State: %d\r\n", huart4.RxState);
                error_count++;

                // 如果中断模式频繁出错，切换到DMA模式
                if(error_count >= 3) {
                    printf("Too many UART errors, switching to DMA mode\r\n");
                    switch_to_dma_mode();
                    error_count = 0;
                } else {
                    uart_restart_receive();
                }
            } else {
                error_count = 0; // 重置错误计数
            }
        } else {
            // 检查DMA模式状态
            if(huart4.RxState != HAL_UART_STATE_BUSY_RX) {
                printf("DMA RX not active! Restarting DMA...\r\n");
                switch_to_dma_mode();
            }
        }
    }
}

// printf重定向到UART4
#include <stdio.h>
struct __FILE
{
  int handle;
};
FILE __stdout;

int fputc(int ch, FILE *f)
{
  HAL_StatusTypeDef status;
  status = HAL_UART_Transmit(&huart4, (uint8_t *)&ch, 1, 100);
  if(status != HAL_OK) {
    // 如果发送失败，可以在这里处理错误
  }
  return ch;
}

// 调试计数器
uint32_t debug_uart_rx_count = 0;
uint32_t debug_frame_complete_count = 0;
uint32_t debug_parse_success_count = 0;
uint32_t debug_parse_error_count = 0;

// 添加调试LED指示函数（如果有LED的话）
void debug_toggle_led(void)
{
  // 如果有LED，可以在这里切换LED状态来指示程序运行
  // HAL_GPIO_TogglePin(LED_GPIO_Port, LED_Pin);
}

// 调试信息输出函数
void print_debug_info(void)
{
  printf("=== Debug Info ===\r\n");
  printf("UART Mode: %s\r\n", use_dma_mode ? "DMA" : "Interrupt");
  printf("UART State: %d\r\n", huart4.RxState);
  printf("UART RX Count: %lu\r\n", debug_uart_rx_count);
  printf("Frame Complete: %lu\r\n", debug_frame_complete_count);
  printf("Parse Success: %lu\r\n", debug_parse_success_count);
  printf("Parse Error: %lu\r\n", debug_parse_error_count);
  printf("Buffer Index: %d\r\n", buffer_index);
  printf("RX Pointer: %d\r\n", rx_pointer);
  printf("Frame Complete Flag: %d\r\n", frame_complete);
  printf("Error Flags: uart_error=%d, restart=%d\r\n", uart_error_flag, uart_restart_flag);

  if(use_dma_mode) {
    printf("DMA Head: %d, Tail: %d\r\n", dma_get_received_count(), dma_rx_tail);
  }

  printf("==================\r\n");
}

// 测试函数：发送测试数据
void send_test_data(void)
{
  printf("Sending test data...\r\n");
  // 模拟接收测试数据
  char test_data[] = "25.5,30.0,120.5";
  strcpy((char*)rx_buff, test_data);
  rx_pointer = strlen(test_data);
  frame_complete = 1;
  printf("Test data prepared: %s\r\n", rx_buff);
}

// 处理接收到的命令
void process_command(char* cmd)
{
    if(strncmp(cmd, "DEBUG", 5) == 0) {
        print_debug_info();
    }
    else if(strncmp(cmd, "TEST", 4) == 0) {
        send_test_data();
    }
    else if(strncmp(cmd, "CLEAR", 5) == 0) {
        clear_data_buffer();
    }
    else if(strncmp(cmd, "SEND_ALL", 8) == 0) {
        send_all_stored_data();
    }
    else if(strncmp(cmd, "COUNT", 5) == 0) {
        char count_msg[32];
        snprintf(count_msg, sizeof(count_msg), "COUNT:%d\r\n", buffer_index);
        HAL_UART_Transmit(&huart4, (uint8_t*)count_msg, strlen(count_msg), 100);
        printf("Data count sent: %d\r\n", buffer_index);
    }
    else if(strncmp(cmd, "DMA", 3) == 0) {
        switch_to_dma_mode();
    }
    else if(strncmp(cmd, "RESTART", 7) == 0) {
        uart_restart_receive();
    }
    else {
        // 未知命令，发送错误响应
        char error_msg[] = "ERROR:UNKNOWN_COMMAND\r\n";
        HAL_UART_Transmit(&huart4, (uint8_t*)error_msg, strlen(error_msg), 100);
        printf("Unknown command: %s\r\n", cmd);
    }
}

// 中断标志变量
volatile uint8_t uart_error_flag = 0;
volatile uint8_t uart_restart_flag = 0;

// DMA接收缓冲区（循环缓冲区）
#define DMA_RX_BUF_SIZE 256
uint8_t dma_rx_buffer[DMA_RX_BUF_SIZE];
volatile uint16_t dma_rx_head = 0;
volatile uint16_t dma_rx_tail = 0;
uint8_t use_dma_mode = 0; // DMA模式标志

// 切换到DMA接收模式
void switch_to_dma_mode(void)
{
    printf("Switching to DMA receive mode...\r\n");

    // 停止中断接收
    HAL_UART_AbortReceive_IT(&huart4);

    // 启动DMA循环接收
    HAL_StatusTypeDef status = HAL_UART_Receive_DMA(&huart4, dma_rx_buffer, DMA_RX_BUF_SIZE);

    if(status == HAL_OK) {
        use_dma_mode = 1;
        dma_rx_head = 0;
        dma_rx_tail = 0;
        printf("DMA mode activated successfully\r\n");
    } else {
        printf("Failed to activate DMA mode: %d\r\n", status);
        // 回退到中断模式
        uart_restart_receive();
    }
}

// 从DMA缓冲区读取数据
uint16_t dma_get_received_count(void)
{
    return DMA_RX_BUF_SIZE - __HAL_DMA_GET_COUNTER(huart4.hdmarx);
}

// 处理DMA接收的数据
void process_dma_data(void)
{
    uint16_t current_head = dma_get_received_count();

    while(dma_rx_tail != current_head) {
        uint8_t data = dma_rx_buffer[dma_rx_tail];
        dma_rx_tail = (dma_rx_tail + 1) % DMA_RX_BUF_SIZE;

        debug_uart_rx_count++;

        // 记录数据帧开始时间
        if(rx_pointer == 0) {
            frame_start_tick = uwTick;
        }

        // 检查缓冲区溢出
        if(rx_pointer >= RX_BUF_SIZE - 1) {
            uart_error_flag = 1;
            rx_pointer = 0;
            frame_complete = 0;
            memset(rx_buff, 0, RX_BUF_SIZE);
            continue;
        }

        // 存储数据到缓冲区
        rx_buff[rx_pointer] = data;

        // 检查帧结束符
        if(data == FRAME_END_CHAR || data == '\r') {
            rx_buff[rx_pointer] = '\0';
            frame_complete = 1;
            debug_frame_complete_count++;
            break; // 处理完一帧就退出
        } else {
            rx_pointer++;
        }
    }
}

void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if(huart->Instance == UART4) {
        debug_uart_rx_count++; // 调试计数

        // 记录数据帧开始时间
        if(rx_pointer == 0) {
            frame_start_tick = uwTick;
        }

        // 检查缓冲区溢出
        if(rx_pointer >= RX_BUF_SIZE - 1) {
            // 不在中断中调用printf，设置标志
            uart_error_flag = 1;
            rx_pointer = 0;
            frame_complete = 0;
            memset(rx_buff, 0, RX_BUF_SIZE);
        } else {
            // 存储数据到缓冲区
            rx_buff[rx_pointer] = rx_data;

            // 检查帧结束符
            if(rx_data == FRAME_END_CHAR || rx_data == '\r') {
                rx_buff[rx_pointer] = '\0'; // 字符串结束符
                frame_complete = 1;
                debug_frame_complete_count++; // 调试计数
            } else {
                rx_pointer++;
            }
        }

        // 安全地重启接收，检查UART状态
        HAL_StatusTypeDef status = HAL_UART_Receive_IT(&huart4, &rx_data, 1);
        if(status != HAL_OK) {
            // 如果失败，设置重启标志
            uart_restart_flag = 1;
        }
    }
}

// UART错误回调函数
void HAL_UART_ErrorCallback(UART_HandleTypeDef *huart)
{
    if(huart->Instance == UART4) {
        // 清除错误标志
        __HAL_UART_CLEAR_FLAG(huart, UART_CLEAR_OREF | UART_CLEAR_FECF |
                              UART_CLEAR_PECF | UART_CLEAR_NECF);

        // 重置接收状态
        rx_pointer = 0;
        frame_complete = 0;
        memset(rx_buff, 0, RX_BUF_SIZE);

        // 设置重启标志
        uart_restart_flag = 1;
        uart_error_flag = 2; // 错误类型2
    }
}
