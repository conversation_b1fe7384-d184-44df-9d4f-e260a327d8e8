#include "headfile.h"
#define DATA_INPUT_USER  10      // ÿ��������󳤶�
#define AXIS_NUMBER      3       // ����ά�ȣ���ǰ�¶�,�趨�¶�,ʱ�䣩
#define RX_BUF_SIZE      128     // ���ջ�������С
#define FRAME_END_CHAR   '\n'    // ���ݰ�������
#define FRAME_TIMEOUT_MS 100     // ���ݰ���ʱʱ��(ms)

// ȫ�ֱ���
uint32_t uart_tick = 0;          // ���ڴ���ʱ���
uint32_t frame_start_tick = 0;   // ���ݰ���ʼʱ���
uint8_t rx_data = 0;             // ���ֽڽ��ջ���
uint8_t rx_buff[RX_BUF_SIZE];    // ���ջ�����
uint16_t rx_pointer = 0;         // ������ָ��
uint8_t data_ready = 0;          // ���ݾ�����־
uint8_t frame_complete = 0;      // ���ݰ������־
float input_user_buffer[DATA_INPUT_USER * AXIS_NUMBER]; // ���ݴ洢����
uint16_t buffer_index = 0;       // �洢����������
float temperature = 0.0f;
float setting = 0.0f;
float time_value = 0.0f;
// ���ݴ洢����input_user_buffer
void store_data_to_buffer(void)
{
    if (buffer_index < DATA_INPUT_USER) {
        uint16_t base_index = buffer_index * AXIS_NUMBER;
        input_user_buffer[base_index + 0] = temperature;  // ��ǰ�¶�
        input_user_buffer[base_index + 1] = setting;      // �趨�¶�
        input_user_buffer[base_index + 2] = time_value;   // ʱ��ֵ
        buffer_index++;
        printf("Data stored [%d]: T=%.1f, Set=%.1f, Time=%.1f\r\n",
               buffer_index-1, temperature, setting, time_value);
    } else {
        printf("Buffer full! Cannot store more data.\r\n");
    }
}

// ��ȡ�洢������
uint16_t get_stored_data_count(void)
{
    return buffer_index;
}

// ��ջ�����
void clear_data_buffer(void)
{
    buffer_index = 0;
    memset(input_user_buffer, 0, sizeof(input_user_buffer));
    printf("Data buffer cleared.\r\n");
}

uint8_t parse_data(void)
{
    char *token;
    char *endptr;
    char temp_buff[RX_BUF_SIZE]; // ��ʱ������ֹ�ƻ�ԭʼ����

    // ������ԭʼ����
    strncpy(temp_buff, (char*)rx_buff, RX_BUF_SIZE-1);
    temp_buff[RX_BUF_SIZE-1] = '\0';

    // ������һ��ֵ (��ǰ�¶�)
    token = strtok(temp_buff, ",");
    if (!token) return 0;
    temperature = strtof(token, &endptr);
    if (*endptr != '\0') return 0; // ��������

    // �����ڶ���ֵ (�趨�¶�)
    token = strtok(NULL, ",");
    if (!token) return 0;
    setting = strtof(token, &endptr);
    if (*endptr != '\0') return 0;

    // ����������ֵ (ʱ��)
    token = strtok(NULL, ",");
    if (!token) return 0;
    time_value = strtof(token, &endptr);
    if (*endptr != '\0') return 0;

    return 1; // �����ɹ�
}

void uart_proc(void)
{
    // ÿ10ms���һ��
    if(uwTick - uart_tick < 10) {
        return;
    }
    uart_tick = uwTick;

    // ���ݰ���ʱ����
    if(rx_pointer > 0 && (uwTick - frame_start_tick > FRAME_TIMEOUT_MS)) {
        printf("Frame timeout! Clearing buffer.\r\n");
        rx_pointer = 0;
        frame_complete = 0;
        memset(rx_buff, 0, RX_BUF_SIZE);
        return;
    }

    // ������ݰ�����
    if(frame_complete) {
        printf("Received: %s\r\n", rx_buff);

        if (parse_data()) {
            printf("Parsed: T=%.1f°C, Set=%.1f°C, Time=%.1fs\r\n",
                   temperature, setting, time_value);

            // �洢���ݵ�����
            store_data_to_buffer();

        } else {
            printf("ERROR: Invalid data format\r\n");
        }

        // ���ý���״̬
        rx_pointer = 0;
        frame_complete = 0;
        memset(rx_buff, 0, RX_BUF_SIZE);
    }
}

#include <stdio.h>
struct __FILE
{
  int handle;
  /* Whatever you require here. If the only file you are using is */
  /* standard output using printf() for debugging, no file handling */
  /* is required. */
};
/* FILE is typedef��d in stdio.h. */
FILE __stdout;
int fputc(int ch, FILE *f) 
{
  HAL_UART_Transmit(&huart4,(uint8_t *)&ch,1,50);/* Your implementation of fputc(). */
  return ch;
}

void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if(huart->Instance == UART4) {
        // ��¼���ݰ���ʼʱ��
        if(rx_pointer == 0) {
            frame_start_tick = uwTick;
        }

        // ��黺������Խ��
        if(rx_pointer >= RX_BUF_SIZE - 1) {
            printf("Buffer overflow! Resetting...\r\n");
            rx_pointer = 0;
            frame_complete = 0;
            memset(rx_buff, 0, RX_BUF_SIZE);
        } else {
            // �洢���ݵ�����
            rx_buff[rx_pointer] = rx_data;

            // ������������
            if(rx_data == FRAME_END_CHAR || rx_data == '\r') {
                rx_buff[rx_pointer] = '\0'; // �ַ�������
                frame_complete = 1;
            } else {
                rx_pointer++;
            }
        }

        // ������һ���ֽ�
        HAL_UART_Receive_IT(&huart4, &rx_data, 1);
    }
}
