#include "headfile.h"
#define DATA_INPUT_USER  10      // ÿ��������󳤶�
#define AXIS_NUMBER      3       // ����ά�ȣ���ǰ�¶�,�趨�¶�,ʱ�䣩
#define RX_BUF_SIZE      128     // ���ջ�������С

// ȫ�ֱ���
uint32_t uart_tick = 0;          // ���ڴ���ʱ���
uint8_t rx_data = 0;             // ���ֽڽ��ջ���
uint8_t rx_buff[RX_BUF_SIZE];    // ���ջ�����
uint16_t rx_pointer = 0;         // ������ָ��
uint8_t data_ready = 0;          // ���ݾ�����־
float input_user_buffer[DATA_INPUT_USER * AXIS_NUMBER]; // ���ݴ洢����
uint16_t buffer_index = 0;       // �洢����������
float temperature = 0.0f;
float setting = 0.0f;
float time_value = 0.0f;
uint8_t parse_data(void)
{
    char *token;
    char *endptr;
    
    // ������һ��ֵ (��ǰ�¶�)
    token = strtok((char*)rx_buff, ",");
    if (!token) return 0;
    temperature = strtof(token, &endptr);
    if (*endptr != '\0') return 0; // ��������
    
    // �����ڶ���ֵ (�趨�¶�)
    token = strtok(NULL, ",");
    if (!token) return 0;
    setting = strtof(token, &endptr);
    if (*endptr != '\0') return 0;
    
    // ����������ֵ (ʱ��)
    token = strtok(NULL, ",");
    if (!token) return 0;
    time_value = strtof(token, &endptr);
    if (*endptr != '\0') return 0;
    
    return 1; // �����ɹ�
}

void uart_proc(void)
{
	if(uwTick - uart_tick < 50)
	{
		return;
	}
	uart_tick = uwTick;
		if(rx_pointer == 3)
		{
        printf("Received: %s\r\n", rx_buff); // ��ʾԭʼ����
        if (parse_data()) 
			{
            printf("Parsed: T=%.1f��C, Set=%.1f��C, Time=%.1fs\r\n", 
                   temperature, setting, time_value);
            
            // �������������Ӧ�ô����߼�
            // ...
        } else 
			{
            printf("ERROR: Invalid format\r\n");
        }
        
        // ���ý���״̬
        rx_pointer = 0;
        // ���������
        memset((void*)rx_buff, 0, RX_BUF_SIZE);
		}
		else if(rx_pointer > 0)
		{
			printf("NULL");
			rx_pointer = 0;
			memset(&rx_buff,0,sizeof(rx_buff));
			return;
		}
	
}

#include <stdio.h>
struct __FILE
{
  int handle;
  /* Whatever you require here. If the only file you are using is */
  /* standard output using printf() for debugging, no file handling */
  /* is required. */
};
/* FILE is typedef��d in stdio.h. */
FILE __stdout;
int fputc(int ch, FILE *f) 
{
  HAL_UART_Transmit(&huart4,(uint8_t *)&ch,1,50);/* Your implementation of fputc(). */
  return ch;
}

void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
	uart_tick = uwTick;
	HAL_UART_Receive_DMA(&huart4,&rx_data,1);
	rx_buff[rx_pointer++] = rx_data;
}
