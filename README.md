# STM32H743 温度控制系统

## 项目概述
基于STM32H743的温度数据接收、解析和存储系统，支持通过UART接收CSV格式的温度数据并存储到缓冲区中。

## 硬件配置
- **芯片**: STM32H743IITx (Cortex-M7, 400MHz)
- **UART4**: 波特率9600, TX(PA0), RX(PI9)
- **系统时钟**: 400MHz
- **外部晶振**: 25MHz HSE

## 功能特性

### 数据接收与解析
- 支持CSV格式数据接收: `温度,设定值,时间\n`
- 自动检测数据帧结束符(`\n`或`\r`)
- 缓冲区溢出保护机制
- 数据帧超时检测(100ms)
- 数据格式验证和错误处理

### 数据存储
- 存储到`input_user_buffer[DATA_INPUT_USER * AXIS_NUMBER]`数组
- 支持最多10组数据存储
- 每组数据包含3个维度：当前温度、设定温度、时间
- 提供缓冲区状态查询和清空功能

### 调试输出
- 实时显示接收到的原始数据
- 解析结果输出
- 存储状态反馈
- 系统状态定期报告(每5秒)

## 数据格式

### 输入格式
```
25.5,30.0,120.5\n
```
- 第一个值：当前温度(°C)
- 第二个值：设定温度(°C)  
- 第三个值：时间值(秒)
- 结束符：`\n`或`\r`

### 存储格式
数据按以下方式存储在`input_user_buffer`数组中：
```c
// 第i组数据的存储位置
base_index = i * AXIS_NUMBER;
input_user_buffer[base_index + 0] = temperature;  // 当前温度
input_user_buffer[base_index + 1] = setting;      // 设定温度
input_user_buffer[base_index + 2] = time_value;   // 时间值
```

## API接口

### 主要函数
```c
void uart_proc(void);                    // UART数据处理(主循环调用)
void store_data_to_buffer(void);         // 存储数据到缓冲区
uint16_t get_stored_data_count(void);    // 获取已存储数据数量
void clear_data_buffer(void);            // 清空数据缓冲区
uint8_t parse_data(void);                // 解析CSV数据
```

### 全局变量
```c
extern float input_user_buffer[DATA_INPUT_USER * AXIS_NUMBER]; // 数据存储缓冲区
extern uint16_t buffer_index;            // 当前存储索引
extern float temperature, setting, time_value; // 最新解析的数据
```

## 配置参数
```c
#define DATA_INPUT_USER  10      // 最大存储组数
#define AXIS_NUMBER      3       // 每组数据维度
#define RX_BUF_SIZE      128     // 接收缓冲区大小
#define FRAME_TIMEOUT_MS 100     // 数据帧超时时间
```

## 使用示例

### 基本使用
1. 系统启动后自动初始化UART中断接收
2. 通过串口发送数据: `25.5,30.0,120.5\n`
3. 系统自动解析并存储数据
4. 可通过`get_stored_data_count()`查询存储状态

### 数据读取
```c
uint16_t count = get_stored_data_count();
for(int i = 0; i < count; i++) {
    uint16_t base = i * AXIS_NUMBER;
    float temp = input_user_buffer[base + 0];
    float set = input_user_buffer[base + 1]; 
    float time = input_user_buffer[base + 2];
    printf("Data[%d]: T=%.1f, Set=%.1f, Time=%.1f\r\n", i, temp, set, time);
}
```

## 错误处理

### 数据接收错误
- 缓冲区溢出：自动重置接收状态
- 数据帧超时：清空当前接收缓冲区
- 格式错误：显示错误信息，继续接收下一帧

### 存储错误
- 缓冲区满：显示警告信息，停止存储新数据
- 可通过`clear_data_buffer()`清空缓冲区重新开始

## 性能优化

### 接收优化
- 使用中断方式接收，减少CPU占用
- 10ms处理间隔，平衡响应速度和系统负载
- 临时缓冲区保护原始数据完整性

### 存储优化
- 直接存储到目标数组，避免多次拷贝
- 索引管理简化数据访问
- 边界检查防止数组越界

## 调试信息输出示例
```
=== STM32H743 Temperature Control System ===
Ready to receive data in format: temp,setting,time\n

Received: 25.5,30.0,120.5
Parsed: T=25.5°C, Set=30.0°C, Time=120.5s
Data stored [0]: T=25.5, Set=30.0, Time=120.5
Status: 1 data sets stored in buffer
Latest data - T:25.5°C, Set:30.0°C, Time:120.5s
```

## 编译和使用
1. 使用Keil MDK-ARM V5.27编译
2. 下载到STM32H743开发板
3. 通过串口调试工具发送测试数据
4. 观察串口输出的调试信息

## 注意事项
- 确保发送数据以`\n`或`\r`结尾
- 数据格式必须严格按照CSV格式
- 缓冲区满时需要手动清空或重启系统
- 建议在实际应用中添加数据持久化存储功能
