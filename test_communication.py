#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
STM32H743 温度数据通信测试脚本
测试数据接收、存储和发送功能
"""

import serial
import time
import random

class STM32Tester:
    def __init__(self, port='COM3', baudrate=9600, timeout=2):
        """初始化串口连接"""
        try:
            self.ser = serial.Serial(port, baudrate, timeout=timeout)
            print(f"串口 {port} 连接成功，波特率: {baudrate}")
            time.sleep(2)  # 等待STM32启动
        except Exception as e:
            print(f"串口连接失败: {e}")
            raise
    
    def send_command(self, cmd, wait_time=0.5):
        """发送命令并接收响应"""
        print(f"\n>>> 发送命令: {cmd}")
        self.ser.write((cmd + '\n').encode())
        time.sleep(wait_time)
        
        responses = []
        while self.ser.in_waiting:
            response = self.ser.readline().decode().strip()
            if response:
                responses.append(response)
                print(f"<<< 接收: {response}")
        
        return responses
    
    def send_data(self, temp, setting, time_val, wait_time=0.5):
        """发送温度数据"""
        data = f"{temp:.1f},{setting:.1f},{time_val:.1f}"
        print(f"\n>>> 发送数据: {data}")
        self.ser.write((data + '\n').encode())
        time.sleep(wait_time)
        
        responses = []
        while self.ser.in_waiting:
            response = self.ser.readline().decode().strip()
            if response:
                responses.append(response)
                print(f"<<< 接收: {response}")
        
        return responses
    
    def test_basic_data_sending(self):
        """测试基本数据发送功能"""
        print("\n" + "="*50)
        print("测试1: 基本数据发送功能")
        print("="*50)
        
        test_data = [
            (25.5, 30.0, 120.5),
            (22.3, 25.0, 90.2),
            (28.7, 32.5, 150.8),
            (20.1, 22.0, 60.3),
            (35.2, 40.0, 200.1)
        ]
        
        for i, (temp, setting, time_val) in enumerate(test_data):
            print(f"\n--- 发送第 {i+1} 组数据 ---")
            responses = self.send_data(temp, setting, time_val)
            
            # 检查是否收到ACK
            ack_received = any("ACK:" in resp for resp in responses)
            if ack_received:
                print("✓ 数据发送成功，收到确认")
            else:
                print("✗ 数据发送失败，未收到确认")
    
    def test_commands(self):
        """测试命令功能"""
        print("\n" + "="*50)
        print("测试2: 命令功能测试")
        print("="*50)
        
        commands = [
            ("COUNT", "查询数据数量"),
            ("DEBUG", "获取调试信息"),
            ("SEND_ALL", "获取所有数据"),
            ("TEST", "发送测试数据"),
        ]
        
        for cmd, desc in commands:
            print(f"\n--- {desc} ---")
            self.send_command(cmd, wait_time=1.0)
    
    def test_buffer_overflow(self):
        """测试缓冲区溢出处理"""
        print("\n" + "="*50)
        print("测试3: 缓冲区溢出测试")
        print("="*50)
        
        # 先清空缓冲区
        print("清空缓冲区...")
        self.send_command("CLEAR")
        
        # 发送11组数据（超过10组的限制）
        print("发送11组数据测试溢出处理...")
        for i in range(11):
            temp = 20 + i
            setting = 25 + i
            time_val = i * 10
            
            print(f"\n--- 发送第 {i+1} 组数据 ---")
            responses = self.send_data(temp, setting, time_val, wait_time=0.3)
            
            if i >= 10:  # 第11组应该返回BUFFER_FULL
                buffer_full = any("BUFFER_FULL" in resp for resp in responses)
                if buffer_full:
                    print("✓ 缓冲区溢出处理正确")
                else:
                    print("✗ 缓冲区溢出处理异常")
    
    def test_error_handling(self):
        """测试错误处理"""
        print("\n" + "="*50)
        print("测试4: 错误处理测试")
        print("="*50)
        
        error_data = [
            "abc,def,ghi",      # 无效数字
            "25.5,30.0",        # 缺少字段
            "25.5,30.0,120.5,extra",  # 多余字段
            "25.5;30.0;120.5",  # 错误分隔符
            "",                 # 空数据
        ]
        
        for i, data in enumerate(error_data):
            print(f"\n--- 错误数据测试 {i+1}: {data} ---")
            print(f">>> 发送: {data}")
            self.ser.write((data + '\n').encode())
            time.sleep(0.5)
            
            responses = []
            while self.ser.in_waiting:
                response = self.ser.readline().decode().strip()
                if response:
                    responses.append(response)
                    print(f"<<< 接收: {response}")
            
            # 检查是否收到错误响应
            error_received = any("ERROR:" in resp for resp in responses)
            if error_received:
                print("✓ 错误处理正确")
            else:
                print("✗ 错误处理异常")
    
    def test_continuous_sending(self):
        """测试连续发送"""
        print("\n" + "="*50)
        print("测试5: 连续发送测试")
        print("="*50)
        
        # 先清空缓冲区
        self.send_command("CLEAR")
        
        print("连续发送10组数据...")
        success_count = 0
        
        for i in range(10):
            temp = 20 + random.uniform(-5, 15)
            setting = temp + random.uniform(0, 10)
            time_val = i * 12.5
            
            responses = self.send_data(temp, setting, time_val, wait_time=0.2)
            
            # 检查是否成功
            if any("ACK:" in resp for resp in responses):
                success_count += 1
        
        print(f"\n连续发送结果: {success_count}/10 成功")
        
        # 验证存储的数据
        print("\n验证存储的数据...")
        self.send_command("COUNT")
        self.send_command("SEND_ALL", wait_time=2.0)
    
    def test_performance(self):
        """性能测试"""
        print("\n" + "="*50)
        print("测试6: 性能测试")
        print("="*50)
        
        # 先清空缓冲区
        self.send_command("CLEAR")
        
        print("高频发送测试（50ms间隔）...")
        start_time = time.time()
        success_count = 0
        
        for i in range(20):
            temp = 25 + i * 0.5
            setting = 30 + i * 0.5
            time_val = i * 5
            
            self.ser.write(f"{temp:.1f},{setting:.1f},{time_val:.1f}\n".encode())
            time.sleep(0.05)  # 50ms间隔
            
            # 快速检查响应
            if self.ser.in_waiting:
                response = self.ser.readline().decode().strip()
                if "ACK:" in response:
                    success_count += 1
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"性能测试结果:")
        print(f"- 发送时间: {duration:.2f}秒")
        print(f"- 成功率: {success_count}/20 ({success_count/20*100:.1f}%)")
        print(f"- 平均速度: {20/duration:.1f} 数据/秒")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("STM32H743 温度数据通信测试开始")
        print("="*60)
        
        try:
            # 等待系统稳定
            print("等待系统稳定...")
            time.sleep(2)
            
            # 获取初始状态
            print("获取系统初始状态...")
            self.send_command("DEBUG")
            
            # 运行测试
            self.test_basic_data_sending()
            self.test_commands()
            self.test_buffer_overflow()
            self.test_error_handling()
            self.test_continuous_sending()
            self.test_performance()
            
            print("\n" + "="*60)
            print("所有测试完成！")
            
        except Exception as e:
            print(f"测试过程中发生错误: {e}")
        
        finally:
            # 最终状态
            print("\n获取最终系统状态...")
            self.send_command("DEBUG")
    
    def close(self):
        """关闭串口连接"""
        if self.ser and self.ser.is_open:
            self.ser.close()
            print("串口连接已关闭")

def main():
    """主函数"""
    # 配置串口参数（根据实际情况修改）
    PORT = 'COM3'  # Windows: COM3, Linux: /dev/ttyUSB0, macOS: /dev/tty.usbserial-xxx
    BAUDRATE = 9600
    
    try:
        # 创建测试器
        tester = STM32Tester(PORT, BAUDRATE)
        
        # 运行测试
        tester.run_all_tests()
        
    except Exception as e:
        print(f"测试失败: {e}")
    
    finally:
        # 清理资源
        if 'tester' in locals():
            tester.close()

if __name__ == "__main__":
    main()
