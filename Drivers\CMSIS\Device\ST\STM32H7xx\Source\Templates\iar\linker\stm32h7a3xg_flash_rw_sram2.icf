/*###ICF### Section handled by ICF editor, don't touch! ****/
/*-Editor annotation file-*/
/* IcfEditorFile="$TOOLKIT_DIR$\config\ide\IcfEditor\cortex_v1_0.xml" */
/*-Specials-*/
define symbol __ICFEDIT_intvec_start__ = 0x08000000;
/*-Memory Regions-*/
define symbol __ICFEDIT_region_bank1_start__ = 0x08000000;
define symbol __ICFEDIT_region_bank1_end__   = 0x0807FFFF;
define symbol __ICFEDIT_region_bank2_start__ = 0x08100000;
define symbol __ICFEDIT_region_bank2_end__   = 0x0817FFFF;

define symbol __ICFEDIT_region_RAM_start__ = 0x30000000;
define symbol __ICFEDIT_region_RAM_end__   = 0x3001FFFF;

/*-Sizes-*/
define symbol __ICFEDIT_size_cstack__ = 0x1000;
define symbol __ICFEDIT_size_heap__   = 0x200;
/**** End of ICF editor section. ###ICF###*/

define memory mem with size = 4G;
define region ROM_region   = mem:[from __ICFEDIT_region_bank1_start__   to __ICFEDIT_region_bank1_end__] | mem:[ from __ICFEDIT_region_bank2_start__   to __ICFEDIT_region_bank2_end__];
define region RAM_region   = mem:[from __ICFEDIT_region_RAM_start__   to __ICFEDIT_region_RAM_end__];

define block CSTACK    with alignment = 8, size = __ICFEDIT_size_cstack__   { };
define block HEAP      with alignment = 8, size = __ICFEDIT_size_heap__     { };

initialize by copy { readwrite };
do not initialize  { section .noinit };

place at address mem:__ICFEDIT_intvec_start__ { readonly section .intvec };

place in ROM_region   { readonly };
place in RAM_region   { readwrite,
                        block CSTACK, block HEAP };