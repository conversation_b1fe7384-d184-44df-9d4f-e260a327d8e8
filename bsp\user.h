#ifndef _user_h
#define _user_h
#define DATA_INPUT_USER  10      // ÿ��������󳤶�
#define AXIS_NUMBER      3       // ����ά�ȣ���ǰ�¶�,�趨�¶�,ʱ�䣩
#define RX_BUF_SIZE      128     // ���ջ�������С

#include "main.h"

// ȫ�ֱ�������
extern uint8_t rx_data;
extern float input_user_buffer[DATA_INPUT_USER * AXIS_NUMBER];
extern uint16_t buffer_index;
extern float temperature, setting, time_value;

// ��������
extern void uart_proc(void);
extern void store_data_to_buffer(void);
extern uint16_t get_stored_data_count(void);
extern void clear_data_buffer(void);
extern uint8_t parse_data(void);

#endif
