#ifndef _user_h
#define _user_h
#define DATA_INPUT_USER  10      // ÿ��������󳤶�
#define AXIS_NUMBER      3       // ����ά�ȣ���ǰ�¶�,�趨�¶�,ʱ�䣩
#define RX_BUF_SIZE      128     // ���ջ�������С

#include "main.h"

// ȫ�ֱ�������
extern uint8_t rx_data;
extern float input_user_buffer[DATA_INPUT_USER * AXIS_NUMBER];
extern uint16_t buffer_index;
extern float temperature, setting, time_value;

// ��������
extern void uart_proc(void);
extern void store_data_to_buffer(void);
extern uint16_t get_stored_data_count(void);
extern void clear_data_buffer(void);
extern uint8_t parse_data(void);

// 调试函数声明
extern void print_debug_info(void);
extern void send_test_data(void);
extern void debug_toggle_led(void);
extern void uart_restart_receive(void);
extern void switch_to_dma_mode(void);
extern void process_dma_data(void);

// 调试计数器
extern uint32_t debug_uart_rx_count;
extern uint32_t debug_frame_complete_count;
extern uint32_t debug_parse_success_count;
extern uint32_t debug_parse_error_count;

// 中断标志变量
extern volatile uint8_t uart_error_flag;
extern volatile uint8_t uart_restart_flag;

#endif
