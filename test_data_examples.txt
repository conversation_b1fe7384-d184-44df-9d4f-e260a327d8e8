# 测试数据示例

## 正确格式的测试数据
以下数据可以通过串口调试工具发送到STM32H743进行测试：

### 基本测试数据
25.5,30.0,120.5
22.3,25.0,90.2
28.7,32.5,150.8
20.1,22.0,60.3
35.2,40.0,200.1

### 边界值测试
0.0,0.0,0.0
-10.5,5.0,30.0
100.0,95.0,3600.0
999.9,1000.0,9999.9

### 精度测试
25.12345,30.67890,120.55555
22.1,25.9,90.01
28.999,32.001,150.123

## 错误格式示例（用于测试错误处理）

### 缺少字段
25.5,30.0
25.5

### 格式错误
25.5,abc,120.5
abc,30.0,120.5
25.5,30.0,xyz

### 分隔符错误
25.5;30.0;120.5
25.5 30.0 120.5
25.5|30.0|120.5

### 多余字段
25.5,30.0,120.5,extra
25.5,30.0,120.5,200.0,300.0

## 压力测试数据
连续发送以下数据测试系统稳定性：

21.0,25.0,100.0
21.5,25.0,101.0
22.0,25.0,102.0
22.5,25.0,103.0
23.0,25.0,104.0
23.5,25.0,105.0
24.0,25.0,106.0
24.5,25.0,107.0
25.0,25.0,108.0
25.5,25.0,109.0

## 使用说明

### 通过串口调试工具测试
1. 打开串口调试工具（如SecureCRT、Putty等）
2. 配置串口参数：波特率9600，8位数据位，1位停止位，无校验
3. 连接到STM32H743的UART4端口
4. 逐行发送上述测试数据
5. 观察STM32返回的调试信息

### 预期输出示例
发送: 25.5,30.0,120.5
预期输出:
```
Received: 25.5,30.0,120.5
Parsed: T=25.5°C, Set=30.0°C, Time=120.5s
Data stored [0]: T=25.5, Set=30.0, Time=120.5
```

### 错误处理示例
发送: 25.5,abc,120.5
预期输出:
```
Received: 25.5,abc,120.5
ERROR: Invalid data format
```

### 缓冲区满测试
连续发送11组数据（超过DATA_INPUT_USER=10的限制）
第11组数据预期输出:
```
Buffer full! Cannot store more data.
```

## 自动化测试脚本（Python示例）

```python
import serial
import time

# 配置串口
ser = serial.Serial('COM3', 9600, timeout=1)  # 根据实际端口修改

# 测试数据
test_data = [
    "25.5,30.0,120.5\n",
    "22.3,25.0,90.2\n", 
    "28.7,32.5,150.8\n",
    "20.1,22.0,60.3\n",
    "35.2,40.0,200.1\n"
]

# 发送测试数据
for data in test_data:
    print(f"Sending: {data.strip()}")
    ser.write(data.encode())
    time.sleep(0.5)  # 等待处理
    
    # 读取响应
    response = ser.read_all().decode()
    if response:
        print(f"Response: {response}")
    print("-" * 40)

ser.close()
```

## 注意事项
1. 每行数据必须以换行符(\n)结尾
2. 数据发送间隔建议大于100ms，避免数据帧超时
3. 测试前确保STM32系统已正常启动并初始化完成
4. 如果缓冲区满，需要重启系统或发送清空命令（如果实现了该功能）
